package kr.wayplus.qr_hallimpark.service;

import kr.wayplus.qr_hallimpark.common.mapper.AdminListMapper;
import kr.wayplus.qr_hallimpark.common.service.impl.BaseAdminListService;
import kr.wayplus.qr_hallimpark.mapper.ContentMapper;
import kr.wayplus.qr_hallimpark.model.AdminListResponse;
import kr.wayplus.qr_hallimpark.model.AdminListSearch;
import kr.wayplus.qr_hallimpark.model.Content;
import kr.wayplus.qr_hallimpark.model.ContentCategory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 콘텐츠 서비스
 * - 콘텐츠 CRUD 비즈니스 로직 처리
 * - 공통 리스트 기능 지원
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class ContentService extends BaseAdminListService<Content> {
    private final ContentMapper contentMapper;
    private final ContentCategoryService contentCategoryService;

    // ========== 공통 리스트 기능 구현 ==========

    @Override
    protected AdminListMapper<Content> getMapper() {
        return contentMapper;
    }

    @Override
    protected String getTableName() {
        return "content_master";
    }

    @Override
    protected String getDefaultSortField() {
        return "content_id"; // 최신 생성 순으로 정렬 (ID가 높을수록 최신)
    }

    @Override
    public AdminListSearch createDefaultSearchCondition() {
        return AdminListSearch.builder()
                .page(1)
                .size(20)
                .sortField("content_id")
                .sortDirection("DESC") // 내림차순으로 최신 항목이 위로
                .includeDeleted(false) // 삭제된 항목 제외
                .tableName(getTableName())
                .build();
    }

    @Override
    protected void validateDomainSpecificConditions(AdminListSearch searchCondition) {
        // 허용된 검색 필드 검증
        List<String> allowedSearchFields = Arrays.asList("title", "content_code", "category_name", "sub_category_name");
        validateSearchFields(searchCondition, allowedSearchFields);

        // 허용된 정렬 필드 검증
        List<String> allowedSortFields = Arrays.asList("content_id", "title", "content_code", "category_name", "access_level", "create_date", "last_update_date");
        validateSortField(searchCondition, allowedSortFields);
    }

    // ========== 콘텐츠 관련 비즈니스 로직 ==========

    /**
     * 활성화된 콘텐츠 목록 조회 (API용)
     * @param categoryId 카테고리 ID (선택사항)
     * @param page 페이지 번호
     * @param size 페이지 크기
     * @return 페이징된 활성화 콘텐츠 목록
     */
    public AdminListResponse<Content> findActiveContents(Long categoryId, int page, int size) {
        log.debug("Finding active contents - categoryId: {}, page: {}, size: {}", categoryId, page, size);

        AdminListSearch searchCondition = AdminListSearch.builder()
                .page(page)
                .size(Math.min(size, 100)) // 최대 100개로 제한
                .categoryId(categoryId)
                .includeDeleted(false)
                .status("ACTIVE") // 활성화된 콘텐츠만
                .sortField("content_id")
                .sortDirection("DESC")
                .tableName("content_master")
                .build();

        return findListWithConditions(searchCondition);
    }

    /**
     * 콘텐츠 목록 조회 (관리자용 - 검색, 정렬, 페이징 기능 포함)
     * @param params 검색 파라미터 Map
     * @return 콘텐츠 목록과 총 개수를 포함한 응답
     */
    public HashMap<String, Object> findContentsForManagement(Map<String, Object> params) {
        log.debug("Finding contents for management with params: {}", params);

        HashMap<String, Object> response = new HashMap<>();

        try {
            // 데이터 조회
            List<Content> contents = contentMapper.selectActiveContentsForApi(params);
            Long totalCount = contentMapper.countActiveContentsForApi(params);

            // 페이징 정보 계산 (null 체크 추가)
            Integer page = (Integer) params.get("page");
            Integer size = (Integer) params.get("size");

            if (page == null) page = 1;
            if (size == null) size = 20;

            int totalPages = (int) Math.ceil((double) totalCount / size);
            boolean hasNext = page < totalPages;
            boolean hasPrevious = page > 1;

            // 응답 데이터 구성
            HashMap<String, Object> data = new HashMap<>();
            data.put("items", contents);
            data.put("currentPage", page);
            data.put("totalPages", totalPages);
            data.put("totalCount", totalCount);
            data.put("hasNext", hasNext);
            data.put("hasPrevious", hasPrevious);
            data.put("pageSize", size);

            response.put("success", true);
            response.put("data", data);

            log.debug("Successfully retrieved {} contents out of {} total", contents.size(), totalCount);

        } catch (Exception e) {
            log.error("Error retrieving contents for management: {}", e.getMessage(), e);
            response.put("success", false);
            response.put("message", "콘텐츠 목록 조회 중 오류가 발생했습니다.");
            response.put("errorCode", "INTERNAL_ERROR");
        }

        return response;
    }

    /**
     * 콘텐츠 ID로 콘텐츠 조회
     * @param contentId 콘텐츠 ID
     * @return 콘텐츠 정보
     * @throws IllegalArgumentException 콘텐츠가 존재하지 않는 경우
     */
    public Content findContentById(Long contentId) {
        log.debug("Finding content by ID: {}", contentId);

        if (contentId == null) {
            throw new IllegalArgumentException("콘텐츠 ID가 필요합니다.");
        }

        Content content = contentMapper.selectContentById(contentId);
        if (content == null) {
            throw new IllegalArgumentException("존재하지 않는 콘텐츠입니다. ID: " + contentId);
        }

        return content;
    }

    /**
     * 콘텐츠 코드로 콘텐츠 조회
     * @param contentCode 콘텐츠 코드
     * @return 콘텐츠 정보
     * @throws IllegalArgumentException 콘텐츠가 존재하지 않는 경우
     */
    public Content findContentByCode(String contentCode) {
        log.debug("Finding content by code: {}", contentCode);

        if (contentCode == null || contentCode.trim().isEmpty()) {
            throw new IllegalArgumentException("콘텐츠 코드가 필요합니다.");
        }

        Content content = contentMapper.selectContentByCode(contentCode);
        if (content == null) {
            throw new IllegalArgumentException("존재하지 않는 콘텐츠입니다. 코드: " + contentCode);
        }

        return content;
    }

    /**
     * 특정 카테고리의 콘텐츠 목록 조회
     * @param categoryId 카테고리 ID
     * @return 콘텐츠 목록
     */
    public List<Content> findContentsByCategory(Long categoryId) {
        log.debug("Finding contents by category ID: {}", categoryId);

        if (categoryId == null) {
            throw new IllegalArgumentException("카테고리 ID가 필요합니다.");
        }

        return contentMapper.selectContentListByCategoryId(categoryId);
    }

    /**
     * 특정 카테고리와 그 하위 카테고리의 콘텐츠 목록 조회
     * @param categoryId 상위 카테고리 ID
     * @return 콘텐츠 목록
     */
    public List<Content> findContentsWithChildCategories(Long categoryId) {
        log.debug("Finding contents with child categories for category ID: {}", categoryId);

        if (categoryId == null) {
            throw new IllegalArgumentException("카테고리 ID가 필요합니다.");
        }

        return contentMapper.selectContentListByCategoryIdWithChildren(categoryId);
    }

    /**
     * 최상위 카테고리 목록 조회 (콘텐츠 관리용)
     * @return 최상위 카테고리 목록
     */
    public List<ContentCategory> findRootCategoriesForContentManagement() {
        log.debug("Finding root categories for content management");
        return contentCategoryService.findRootCategories();
    }

    /**
     * 하위 카테고리 목록 조회
     * @param parentId 부모 카테고리 ID
     * @return 하위 카테고리 목록
     */
    public List<ContentCategory> findChildCategories(Long parentId) {
        log.debug("Finding child categories for parent ID: {}", parentId);

        if (parentId == null) {
            throw new IllegalArgumentException("부모 카테고리 ID가 필요합니다.");
        }

        return contentCategoryService.findChildCategories(parentId);
    }

    // ========== CRUD 메서드 (향후 구현) ==========

    /**
     * 콘텐츠 등록
     * @param content 콘텐츠 정보
     * @throws IllegalArgumentException 유효하지 않은 데이터인 경우
     */
    @Transactional
    public void createContent(Content content) {
        log.debug("Creating content: {}", content);

        validateContentForCreate(content);

        // 기본값 설정
        if (content.getStatus() == null) {
            content.setStatus("ACTIVE");
        }
        if (content.getAccessLevel() == null) {
            content.setAccessLevel("ALL");
        }
        content.setDeleteYn("N");

        // 임시 콘텐츠 코드 설정 (NOT NULL 제약조건 때문에)
        content.setContentCode("TEMP_" + System.currentTimeMillis());

        // 콘텐츠 등록 (ID 생성을 위해 먼저 등록)
        int result = contentMapper.insertContent(content);
        if (result != 1) {
            throw new RuntimeException("콘텐츠 등록에 실패했습니다.");
        }

        // 콘텐츠 코드 자동 생성 및 업데이트
        String generatedCode = generateContentCode(content.getContentId());
        content.setContentCode(generatedCode);

        int updateResult = contentMapper.updateContentCode(content.getContentId(), generatedCode);
        if (updateResult != 1) {
            throw new RuntimeException("콘텐츠 코드 업데이트에 실패했습니다.");
        }

        log.info("Content created successfully. ID: {}, Code: {}", content.getContentId(), content.getContentCode());
    }

    /**
     * 콘텐츠 수정
     * @param content 콘텐츠 정보
     * @throws IllegalArgumentException 유효하지 않은 데이터인 경우
     */
    @Transactional
    public void updateContent(Content content) {
        log.debug("Updating content: {}", content);

        validateContentForUpdate(content);

        // 기존 콘텐츠 존재 여부 확인 및 콘텐츠 코드 보존
        Content existingContent = findContentById(content.getContentId());

        // 콘텐츠 코드는 변경 불가능하므로 기존 값으로 설정
        content.setContentCode(existingContent.getContentCode());

        int result = contentMapper.updateContent(content);
        if (result != 1) {
            throw new RuntimeException("콘텐츠 수정에 실패했습니다.");
        }

        log.info("Content updated successfully. ID: {}, Code: {}", content.getContentId(), content.getContentCode());
    }

    /**
     * 콘텐츠 상태 변경
     * @param contentId 콘텐츠 ID
     * @param status 새로운 상태
     * @param lastUpdateId 수정자 ID
     */
    @Transactional
    public void updateContentStatus(Long contentId, String status, String lastUpdateId) {
        log.debug("Updating content status. ID: {}, Status: {}, UpdatedBy: {}", contentId, status, lastUpdateId);

        // 기존 콘텐츠 존재 여부 확인
        findContentById(contentId);

        // 상태 유효성 검사
        if (!"ACTIVE".equals(status) && !"INACTIVE".equals(status)) {
            throw new IllegalArgumentException("유효하지 않은 상태입니다: " + status);
        }

        int result = contentMapper.updateContentStatus(contentId, status, lastUpdateId);
        if (result != 1) {
            throw new RuntimeException("콘텐츠 상태 변경에 실패했습니다.");
        }

        log.info("Content status updated successfully. ID: {}, Status: {}", contentId, status);
    }

    /**
     * 콘텐츠 코드 자동 생성
     * 규칙: Q + ID값 (최소 5자리, 9999 초과 시 6자리)
     * 예: Q00001, Q00123, Q123456
     *
     * @param contentId 콘텐츠 ID
     * @return 생성된 콘텐츠 코드
     */
    private String generateContentCode(Long contentId) {
        if (contentId == null) {
            throw new IllegalArgumentException("콘텐츠 ID가 필요합니다.");
        }

        // ID 값에 따라 자릿수 결정
        int digits = contentId > 99999 ? 6 : 5;
        String format = "Q%0" + digits + "d";

        return String.format(format, contentId);
    }

    /**
     * 콘텐츠 삭제 (논리 삭제)
     * @param contentId 콘텐츠 ID
     * @param deleteId 삭제자 ID
     */
    @Transactional
    public void deleteContent(Long contentId, String deleteId) {
        log.debug("Deleting content. ID: {}, DeletedBy: {}", contentId, deleteId);

        // 기존 콘텐츠 존재 여부 확인
        findContentById(contentId);

        int result = contentMapper.deleteContent(contentId, deleteId);
        if (result != 1) {
            throw new RuntimeException("콘텐츠 삭제에 실패했습니다.");
        }

        log.info("Content deleted successfully. ID: {}", contentId);
    }

    // ========== 유효성 검사 메서드 ==========

    /**
     * 콘텐츠 등록 시 유효성 검사
     * @param content 콘텐츠 정보
     */
    private void validateContentForCreate(Content content) {
        if (content == null) {
            throw new IllegalArgumentException("콘텐츠 정보가 필요합니다.");
        }

        if (content.getTitle() == null || content.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("콘텐츠 제목이 필요합니다.");
        }

        if (content.getCreateId() == null || content.getCreateId().trim().isEmpty()) {
            throw new IllegalArgumentException("생성자 정보가 필요합니다.");
        }
    }

    /**
     * 콘텐츠 수정 시 유효성 검사
     * @param content 콘텐츠 정보
     */
    private void validateContentForUpdate(Content content) {
        if (content == null) {
            throw new IllegalArgumentException("콘텐츠 정보가 필요합니다.");
        }

        if (content.getContentId() == null) {
            throw new IllegalArgumentException("콘텐츠 ID가 필요합니다.");
        }

        if (content.getTitle() == null || content.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("콘텐츠 제목이 필요합니다.");
        }

        if (content.getLastUpdateId() == null || content.getLastUpdateId().trim().isEmpty()) {
            throw new IllegalArgumentException("수정자 정보가 필요합니다.");
        }
    }
}
