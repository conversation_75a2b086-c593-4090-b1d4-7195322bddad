<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/manage}" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <title th:text="${pageTitle} + ' - 한림공원 관리시스템'">콘텐츠 관리 - 한림공원 관리시스템</title>
    <meta name="description" th:content="${pageDescription}">
</head>
<th:block layout:fragment="head">
    <link href="/css/manage/common/admin-list.css" rel="stylesheet">
    <link href="/css/manage/content/content.css" rel="stylesheet">
    <link href="/css/manage/content/form.css" rel="stylesheet">
</th:block>
<body>
    <div layout:fragment="content">
        <!-- 페이지 헤더 -->
        <section class="content-page-header">
            <div class="container">
                <div class="row">
                    <div class="col-md-8">
                        <h2 th:text="${pageTitle}">콘텐츠 등록</h2>
                        <p th:text="${pageDescription}">새로운 콘텐츠를 등록합니다.</p>
                    </div>
                    <div class="col-md-4">
                        <a th:href="@{/manage/content/list}" class="btn btn-secondary">
                            <i class="fas fa-list"></i> 목록으로
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 메인 콘텐츠 -->
        <section>
            <div class="container">
                <!-- 성공 메시지 -->
                <div th:if="${successMessage}" class="alert alert-success" role="alert">
                    <span th:text="${successMessage}">성공 메시지</span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- 에러 메시지 -->
                <div th:if="${errorMessage}" class="alert alert-danger" role="alert">
                    <span th:text="${errorMessage}">에러 메시지</span>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>

                <!-- 콘텐츠 등록/수정 폼 -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-file-alt"></i>
                            <span th:text="${isEdit ? '콘텐츠 수정' : '콘텐츠 등록'}">콘텐츠 등록</span>
                        </h5>
                    </div>
                    <div class="card-body">
                        <form id="contentForm" th:object="${content}" method="post">
                            <!-- CSRF 토큰 -->
                            <input type="hidden" th:name="${_csrf.parameterName}" th:value="${_csrf.token}"/>
                            
                            <!-- 수정 시 콘텐츠 ID -->
                            <input type="hidden" th:if="${isEdit}" th:field="*{contentId}"/>

                            <div class="row">
                                <!-- 콘텐츠명 -->
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="title" class="form-label">
                                            콘텐츠명 <span class="text-danger">*</span>
                                        </label>
                                        <input type="text" 
                                               class="form-control" 
                                               id="title" 
                                               th:field="*{title}"
                                               placeholder="콘텐츠명을 입력하세요"
                                               required>
                                        <div class="invalid-feedback">
                                            콘텐츠명을 입력해주세요.
                                        </div>
                                    </div>
                                </div>

                                <!-- 콘텐츠 코드 -->
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="contentCode" class="form-label">
                                            콘텐츠 코드
                                        </label>
                                        <input type="text"
                                               class="form-control"
                                               id="contentCode"
                                               th:field="*{contentCode}"
                                               th:placeholder="${isEdit} ? '' : '등록 시 자동 생성됩니다'"
                                               readonly>
                                        <div class="form-text">
                                            <span th:if="${isEdit}">콘텐츠 코드는 등록 후 변경할 수 없습니다.</span>
                                            <span th:unless="${isEdit}">콘텐츠 등록 시 자동으로 생성됩니다. (형식: Q00000)</span>
                                        </div>
                                        <div class="invalid-feedback">
                                            콘텐츠 코드를 입력해주세요.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <!-- 대분류 카테고리 -->
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="categoryId" class="form-label">대분류 카테고리</label>
                                        <select class="form-select" id="categoryId" th:field="*{categoryId}">
                                            <option value="">카테고리를 선택하세요</option>
                                            <option th:each="category : ${rootCategories}" 
                                                    th:value="${category.categoryId}" 
                                                    th:text="${category.categoryName}">
                                                카테고리명
                                            </option>
                                        </select>
                                    </div>
                                </div>

                                <!-- 소분류 카테고리 -->
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="subCategoryId" class="form-label">소분류 카테고리</label>
                                        <select class="form-select" id="subCategoryId" th:field="*{subCategoryId}" disabled>
                                            <option value="">먼저 대분류를 선택하세요</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <!-- 상태 -->
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="status" class="form-label">상태</label>
                                        <select class="form-select" id="status" th:field="*{status}">
                                            <option value="ACTIVE">활성</option>
                                            <option value="INACTIVE">비활성</option>
                                        </select>
                                    </div>
                                </div>

                                <!-- 접근 권한 -->
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="accessLevel" class="form-label">접근 권한</label>
                                        <select class="form-select" id="accessLevel" th:field="*{accessLevel}">
                                            <option value="ALL">전체 접근</option>
                                            <option value="SUPER_ADMIN">슈퍼 관리자</option>
                                            <option value="NORMAL_ADMIN">일반 관리자</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- 버튼 그룹 -->
                            <div class="row">
                                <div class="col-12">
                                    <div class="d-flex justify-content-end gap-2">
                                        <a th:href="@{/manage/content/list}" class="btn btn-secondary">
                                            <i class="fas fa-times"></i> 취소
                                        </a>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save"></i>
                                            <span th:text="${isEdit ? '수정' : '등록'}">등록</span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>
    </div>

    <!-- 추가 스크립트 -->
    <th:block layout:fragment="script">
        <script th:inline="javascript">
            $(document).ready(function() {
                const isEdit = /*[[${isEdit}]]*/ false;
                const contentId = /*[[${content?.contentId}]]*/ null;
                
                // 폼 유효성 검사
                const form = document.getElementById('contentForm');
                
                // 대분류 카테고리 변경 시 소분류 로드
                $('#categoryId').on('change', function() {
                    const categoryId = $(this).val();
                    const subCategorySelect = $('#subCategoryId');
                    
                    if (categoryId) {
                        // 하위 카테고리 조회
                        $.ajax({
                            url: `/manage/content/category/${categoryId}/children`,
                            method: 'GET',
                            success: function(response) {
                                subCategorySelect.empty();
                                subCategorySelect.append('<option value="">소분류를 선택하세요</option>');
                                
                                if (response.success && response.data && response.data.length > 0) {
                                    response.data.forEach(function(category) {
                                        subCategorySelect.append(`<option value="${category.categoryId}">${category.categoryName}</option>`);
                                    });
                                    subCategorySelect.prop('disabled', false);
                                } else {
                                    subCategorySelect.append('<option value="">하위 카테고리가 없습니다</option>');
                                    subCategorySelect.prop('disabled', true);
                                }
                            },
                            error: function() {
                                subCategorySelect.empty();
                                subCategorySelect.append('<option value="">하위 카테고리 로드 실패</option>');
                                subCategorySelect.prop('disabled', true);
                            }
                        });
                    } else {
                        subCategorySelect.empty();
                        subCategorySelect.append('<option value="">먼저 대분류를 선택하세요</option>');
                        subCategorySelect.prop('disabled', true);
                    }
                });
                
                // 콘텐츠 코드는 항상 읽기 전용이므로 유효성 검사 불필요
                
                // 폼 제출 처리
                form.addEventListener('submit', function(e) {
                    e.preventDefault();
                    
                    if (!form.checkValidity()) {
                        e.stopPropagation();
                        form.classList.add('was-validated');
                        return;
                    }
                    
                    const formData = new FormData(form);
                    const contentData = Object.fromEntries(formData.entries());
                    
                    // 빈 값 처리
                    if (!contentData.categoryId) delete contentData.categoryId;
                    if (!contentData.subCategoryId) delete contentData.subCategoryId;

                    // 콘텐츠 코드는 항상 서버에서 관리하므로 클라이언트에서 제거
                    delete contentData.contentCode;
                    
                    const url = isEdit ? `/manage/content/${contentId}` : '/manage/content/add';
                    const method = 'POST';
                    
                    $.ajax({
                        url: url,
                        method: method,
                        contentType: 'application/json',
                        data: JSON.stringify(contentData),
                        beforeSend: function(xhr) {
                            if (window.csrfToken) {
                                xhr.setRequestHeader(window.csrfToken.headerName, window.csrfToken.token);
                            }
                        },
                        success: function(response) {
                            if (response.success) {
                                alert(response.message);
                                window.location.href = '/manage/content/list';
                            } else {
                                alert(response.message || '처리 중 오류가 발생했습니다.');
                            }
                        },
                        error: function(xhr) {
                            console.error('Form submission error:', xhr);
                            alert('처리 중 오류가 발생했습니다.');
                        }
                    });
                });
                
                // 수정 모드일 때 기존 소분류 로드
                if (isEdit && /*[[${content?.categoryId}]]*/ null) {
                    $('#categoryId').trigger('change');
                }
            });
        </script>
    </th:block>
</body>
</html>
