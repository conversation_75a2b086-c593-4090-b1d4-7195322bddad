<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.wayplus.qr_hallimpark.mapper.ContentMapper">

    <resultMap id="ContentResultMap" type="kr.wayplus.qr_hallimpark.model.Content">
        <id property="contentId" column="content_id"/>
        <result property="title" column="title"/>
        <result property="categoryId" column="category_id"/>
        <result property="subCategoryId" column="sub_category_id"/>
        <result property="contentCode" column="content_code"/>
        <result property="status" column="status"/>
        <result property="accessLevel" column="access_level"/>
        <result property="createId" column="create_id"/>
        <result property="createDate" column="create_date"/>
        <result property="lastUpdateId" column="last_update_id"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="deleteYn" column="delete_yn"/>
        <result property="deleteId" column="delete_id"/>
        <result property="deleteDate" column="delete_date"/>
        <result property="categoryName" column="category_name"/>
        <result property="subCategoryName" column="sub_category_name"/>
        <result property="categoryPath" column="category_path"/>
        <result property="quizMappingCount" column="quiz_mapping_count"/>
        <result property="qrMappingCount" column="qr_mapping_count"/>
        <result property="languageSupport" column="language_support"/>
        <result property="supportsKorean" column="supports_korean"/>
        <result property="supportsEnglish" column="supports_english"/>
        <result property="supportsJapanese" column="supports_japanese"/>
        <result property="supportsChinese" column="supports_chinese"/>
    </resultMap>

    <!-- 공통 검색 조건 -->
    <sql id="apiSearchConditions">
        <if test="searchKeyword != null and searchKeyword.trim() != ''">
            <choose>
                <when test="selectedSearchField == 'all' or mappedSearchField == null">
                    <!-- 전체 검색 - 주요 필드들에서 검색 -->
                    AND (cm.title LIKE CONCAT('%', #{searchKeyword}, '%')
                         OR cm.content_code LIKE CONCAT('%', #{searchKeyword}, '%')
                         OR cat.category_name LIKE CONCAT('%', #{searchKeyword}, '%')
                         OR sub_cat.category_name LIKE CONCAT('%', #{searchKeyword}, '%'))
                </when>
                <otherwise>
                    <!-- 단일 필드 검색 - Java에서 매핑된 필드 사용 -->
                    AND ${mappedSearchField} LIKE CONCAT('%', #{searchKeyword}, '%')
                </otherwise>
            </choose>
        </if>
    </sql>

    <!-- 공통 정렬 조건 -->
    <sql id="apiOrderBy">
        <choose>
            <when test="mappedSortField != null and sortDirection != null">
                ORDER BY ${mappedSortField} ${sortDirection}
            </when>
            <otherwise>
                ORDER BY cm.content_id DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 활성화된 콘텐츠 목록 조회 -->
    <select id="selectActiveContentsForApi" parameterType="map" resultMap="ContentResultMap">
        SELECT
            cm.content_id,
            cm.title,
            cm.category_id,
            cm.sub_category_id,
            cm.content_code,
            cm.status,
            cm.access_level,
            cm.create_id,
            cm.create_date,
            cm.last_update_id,
            cm.last_update_date,
            cm.delete_yn,
            cm.delete_id,
            cm.delete_date,
            cat.category_name,
            sub_cat.category_name AS sub_category_name,
            CASE
                WHEN cat.category_name IS NOT NULL AND sub_cat.category_name IS NOT NULL
                THEN CONCAT(cat.category_name, ' > ', sub_cat.category_name)
                WHEN cat.category_name IS NOT NULL
                THEN cat.category_name
                ELSE '미분류'
            END AS category_path,
            COALESCE(quiz_count.quiz_mapping_count, 0) AS quiz_mapping_count
        FROM content_master cm
        LEFT JOIN content_category cat ON cm.category_id = cat.category_id AND cat.delete_yn = 'N'
        LEFT JOIN content_category sub_cat ON cm.sub_category_id = sub_cat.category_id AND sub_cat.delete_yn = 'N'
        LEFT JOIN (
            SELECT content_id, COUNT(*) AS quiz_mapping_count
            FROM content_quiz_mapping
            WHERE delete_yn = 'N'
            GROUP BY content_id
        ) quiz_count ON cm.content_id = quiz_count.content_id
        WHERE cm.delete_yn = 'N'
        <if test="status != null and status != ''">
            AND cm.status = #{status}
        </if>

        <!-- 카테고리 필터 -->
        <if test="categoryId != null">
            AND (cm.category_id = #{categoryId} OR cm.sub_category_id = #{categoryId})
        </if>

        <!-- 접근 권한 필터 -->
        <if test="accessLevel != null and accessLevel != ''">
            AND cm.access_level = #{accessLevel}
        </if>

        <!-- 검색 조건 적용 -->
        <include refid="apiSearchConditions"/>

        <!-- 정렬 조건 적용 -->
        <include refid="apiOrderBy"/>

        <!-- 페이징 -->
        <if test="offset != null and size != null">
            LIMIT #{size} OFFSET #{offset}
        </if>
    </select>

    <!-- 활성화된 콘텐츠 총 개수 조회 -->
    <select id="countActiveContentsForApi" parameterType="map" resultType="Long">
        SELECT COUNT(*)
        FROM content_master cm
        LEFT JOIN content_category cat ON cm.category_id = cat.category_id AND cat.delete_yn = 'N'
        LEFT JOIN content_category sub_cat ON cm.sub_category_id = sub_cat.category_id AND sub_cat.delete_yn = 'N'
        WHERE cm.delete_yn = 'N'
        <if test="status != null and status != ''">
            AND cm.status = #{status}
        </if>

        <!-- 카테고리 필터 -->
        <if test="categoryId != null">
            AND (cm.category_id = #{categoryId} OR cm.sub_category_id = #{categoryId})
        </if>

        <!-- 접근 권한 필터 -->
        <if test="accessLevel != null and accessLevel != ''">
            AND cm.access_level = #{accessLevel}
        </if>

        <!-- 검색 조건 적용 -->
        <include refid="apiSearchConditions"/>
    </select>

    <!-- 공통 리스트 조회 (관리자용) -->
    <select id="selectListWithConditions" parameterType="AdminListSearch" resultMap="ContentResultMap">
        SELECT
            cm.content_id,
            cm.title,
            cm.category_id,
            cm.sub_category_id,
            cm.content_code,
            cm.status,
            cm.access_level,
            cm.create_id,
            cm.create_date,
            cm.last_update_id,
            cm.last_update_date,
            cm.delete_yn,
            cm.delete_id,
            cm.delete_date,
            cat.category_name,
            sub_cat.category_name AS sub_category_name,
            CASE
                WHEN cat.category_name IS NOT NULL AND sub_cat.category_name IS NOT NULL
                THEN CONCAT(cat.category_name, ' > ', sub_cat.category_name)
                WHEN cat.category_name IS NOT NULL
                THEN cat.category_name
                ELSE '미분류'
            END AS category_path,
            COALESCE(quiz_count.quiz_mapping_count, 0) AS quiz_mapping_count,
            COALESCE(qr_count.qr_mapping_count, 0) AS qr_mapping_count,
            -- 다국어 지원 여부 (개별)
            CASE WHEN lang_ko.content_id IS NOT NULL THEN 1 ELSE 0 END AS supports_korean,
            CASE WHEN lang_en.content_id IS NOT NULL THEN 1 ELSE 0 END AS supports_english,
            CASE WHEN lang_ja.content_id IS NOT NULL THEN 1 ELSE 0 END AS supports_japanese,
            CASE WHEN lang_zh.content_id IS NOT NULL THEN 1 ELSE 0 END AS supports_chinese,
            -- 다국어 지원 현황 문자열
            CONCAT_WS(', ',
                CASE WHEN lang_ko.content_id IS NOT NULL THEN '한국어' END,
                CASE WHEN lang_en.content_id IS NOT NULL THEN '영어' END,
                CASE WHEN lang_ja.content_id IS NOT NULL THEN '일본어' END,
                CASE WHEN lang_zh.content_id IS NOT NULL THEN '중국어' END
            ) AS language_support
        FROM content_master cm
        LEFT JOIN content_category cat ON cm.category_id = cat.category_id AND cat.delete_yn = 'N'
        LEFT JOIN content_category sub_cat ON cm.sub_category_id = sub_cat.category_id AND sub_cat.delete_yn = 'N'
        LEFT JOIN (
            SELECT content_id, COUNT(*) AS quiz_mapping_count
            FROM content_quiz_mapping
            WHERE delete_yn = 'N'
            GROUP BY content_id
        ) quiz_count ON cm.content_id = quiz_count.content_id
        LEFT JOIN (
            SELECT cm2.content_id, COUNT(DISTINCT qm.qr_code_id) AS qr_mapping_count
            FROM content_master cm2
            INNER JOIN content_quiz_mapping cqm ON cm2.content_id = cqm.content_id AND cqm.delete_yn = 'N'
            INNER JOIN qr_quiz_mapping qm ON cqm.quiz_id = qm.quiz_id AND qm.delete_yn = 'N'
            GROUP BY cm2.content_id
        ) qr_count ON cm.content_id = qr_count.content_id
        -- 다국어 콘텐츠 조인 (각 언어별로)
        LEFT JOIN (
            SELECT DISTINCT cm3.content_id
            FROM content_master cm3
            INNER JOIN content_quiz_mapping cqm3 ON cm3.content_id = cqm3.content_id AND cqm3.delete_yn = 'N'
            INNER JOIN quiz_content qc3 ON cqm3.quiz_id = qc3.quiz_id AND qc3.lang_code = 'ko' AND qc3.delete_yn = 'N'
        ) lang_ko ON cm.content_id = lang_ko.content_id
        LEFT JOIN (
            SELECT DISTINCT cm4.content_id
            FROM content_master cm4
            INNER JOIN content_quiz_mapping cqm4 ON cm4.content_id = cqm4.content_id AND cqm4.delete_yn = 'N'
            INNER JOIN quiz_content qc4 ON cqm4.quiz_id = qc4.quiz_id AND qc4.lang_code = 'en' AND qc4.delete_yn = 'N'
        ) lang_en ON cm.content_id = lang_en.content_id
        LEFT JOIN (
            SELECT DISTINCT cm5.content_id
            FROM content_master cm5
            INNER JOIN content_quiz_mapping cqm5 ON cm5.content_id = cqm5.content_id AND cqm5.delete_yn = 'N'
            INNER JOIN quiz_content qc5 ON cqm5.quiz_id = qc5.quiz_id AND qc5.lang_code = 'ja' AND qc5.delete_yn = 'N'
        ) lang_ja ON cm.content_id = lang_ja.content_id
        LEFT JOIN (
            SELECT DISTINCT cm6.content_id
            FROM content_master cm6
            INNER JOIN content_quiz_mapping cqm6 ON cm6.content_id = cqm6.content_id AND cqm6.delete_yn = 'N'
            INNER JOIN quiz_content qc6 ON cqm6.quiz_id = qc6.quiz_id AND qc6.lang_code = 'zh' AND qc6.delete_yn = 'N'
        ) lang_zh ON cm.content_id = lang_zh.content_id
        WHERE cm.delete_yn = 'N'

        <!-- 추가 검색 조건들 -->
        <if test="searchKeyword != null and searchKeyword.trim() != ''">
            <choose>
                <when test="selectedSearchField == 'all' or selectedSearchField == null or selectedSearchField == ''">
                    AND (cm.title LIKE CONCAT('%', #{searchKeyword}, '%')
                         OR cm.content_code LIKE CONCAT('%', #{searchKeyword}, '%')
                         OR cat.category_name LIKE CONCAT('%', #{searchKeyword}, '%')
                         OR sub_cat.category_name LIKE CONCAT('%', #{searchKeyword}, '%'))
                </when>
                <when test="selectedSearchField == 'title'">
                    AND cm.title LIKE CONCAT('%', #{searchKeyword}, '%')
                </when>
                <when test="selectedSearchField == 'content_code'">
                    AND cm.content_code LIKE CONCAT('%', #{searchKeyword}, '%')
                </when>
                <when test="selectedSearchField == 'category_name'">
                    AND (cat.category_name LIKE CONCAT('%', #{searchKeyword}, '%')
                         OR sub_cat.category_name LIKE CONCAT('%', #{searchKeyword}, '%'))
                </when>
            </choose>
        </if>

        <!-- 카테고리 필터 -->
        <if test="categoryId != null">
            AND (cm.category_id = #{categoryId} OR cm.sub_category_id = #{categoryId})
        </if>

        <!-- 상태 필터 -->
        <if test="status != null and status != ''">
            AND cm.status = #{status}
        </if>

        <!-- 접근 권한 필터 -->
        <if test="filters != null and filters.access_level != null and filters.access_level != ''">
            AND cm.access_level = #{filters.access_level}
        </if>

        <!-- 정렬 조건 -->
        <choose>
            <when test="sortField != null and sortField != '' and sortDirection != null and (sortDirection == 'ASC' or sortDirection == 'DESC')">
                ORDER BY cm.${sortField} ${sortDirection}
            </when>
            <otherwise>
                ORDER BY cm.content_id DESC
            </otherwise>
        </choose>

        <!-- 페이징 -->
        <if test="offset != null and limit != null">
            LIMIT #{offset}, #{limit}
        </if>
    </select>

    <!-- 공통 리스트 개수 조회 (관리자용) -->
    <select id="countWithConditions" parameterType="AdminListSearch" resultType="Long">
        SELECT COUNT(*)
        FROM content_master cm
        LEFT JOIN content_category cat ON cm.category_id = cat.category_id AND cat.delete_yn = 'N'
        LEFT JOIN content_category sub_cat ON cm.sub_category_id = sub_cat.category_id AND sub_cat.delete_yn = 'N'
        WHERE cm.delete_yn = 'N'

        <!-- 추가 검색 조건들 -->
        <if test="searchKeyword != null and searchKeyword.trim() != ''">
            <choose>
                <when test="selectedSearchField == 'all' or selectedSearchField == null or selectedSearchField == ''">
                    AND (cm.title LIKE CONCAT('%', #{searchKeyword}, '%')
                         OR cm.content_code LIKE CONCAT('%', #{searchKeyword}, '%')
                         OR cat.category_name LIKE CONCAT('%', #{searchKeyword}, '%')
                         OR sub_cat.category_name LIKE CONCAT('%', #{searchKeyword}, '%'))
                </when>
                <when test="selectedSearchField == 'title'">
                    AND cm.title LIKE CONCAT('%', #{searchKeyword}, '%')
                </when>
                <when test="selectedSearchField == 'content_code'">
                    AND cm.content_code LIKE CONCAT('%', #{searchKeyword}, '%')
                </when>
                <when test="selectedSearchField == 'category_name'">
                    AND (cat.category_name LIKE CONCAT('%', #{searchKeyword}, '%')
                         OR sub_cat.category_name LIKE CONCAT('%', #{searchKeyword}, '%'))
                </when>
            </choose>
        </if>

        <!-- 카테고리 필터 -->
        <if test="categoryId != null">
            AND (cm.category_id = #{categoryId} OR cm.sub_category_id = #{categoryId})
        </if>

        <!-- 상태 필터 -->
        <if test="status != null and status != ''">
            AND cm.status = #{status}
        </if>

        <!-- 접근 권한 필터 -->
        <if test="filters != null and filters.access_level != null and filters.access_level != ''">
            AND cm.access_level = #{filters.access_level}
        </if>
    </select>

    <!-- 모든 콘텐츠 목록 조회 -->
    <select id="selectContentList" resultMap="ContentResultMap">
        SELECT
            cm.content_id,
            cm.title,
            cm.category_id,
            cm.sub_category_id,
            cm.content_code,
            cm.status,
            cm.access_level,
            cm.create_id,
            cm.create_date,
            cm.last_update_id,
            cm.last_update_date,
            cm.delete_yn,
            cm.delete_id,
            cm.delete_date,
            cat.category_name,
            sub_cat.category_name AS sub_category_name,
            CASE
                WHEN cat.category_name IS NOT NULL AND sub_cat.category_name IS NOT NULL
                THEN CONCAT(cat.category_name, ' > ', sub_cat.category_name)
                WHEN cat.category_name IS NOT NULL
                THEN cat.category_name
                ELSE '미분류'
            END AS category_path,
            COALESCE(quiz_count.quiz_mapping_count, 0) AS quiz_mapping_count
        FROM content_master cm
        LEFT JOIN content_category cat ON cm.category_id = cat.category_id AND cat.delete_yn = 'N'
        LEFT JOIN content_category sub_cat ON cm.sub_category_id = sub_cat.category_id AND sub_cat.delete_yn = 'N'
        LEFT JOIN (
            SELECT content_id, COUNT(*) AS quiz_mapping_count
            FROM content_quiz_mapping
            WHERE delete_yn = 'N'
            GROUP BY content_id
        ) quiz_count ON cm.content_id = quiz_count.content_id
        WHERE cm.delete_yn = 'N'
        ORDER BY cm.content_id DESC
    </select>

    <!-- 콘텐츠 ID로 콘텐츠 조회 -->
    <select id="selectContentById" resultMap="ContentResultMap">
        SELECT
            cm.content_id,
            cm.title,
            cm.category_id,
            cm.sub_category_id,
            cm.content_code,
            cm.status,
            cm.access_level,
            cm.create_id,
            cm.create_date,
            cm.last_update_id,
            cm.last_update_date,
            cm.delete_yn,
            cm.delete_id,
            cm.delete_date,
            cat.category_name,
            sub_cat.category_name AS sub_category_name,
            CASE
                WHEN cat.category_name IS NOT NULL AND sub_cat.category_name IS NOT NULL
                THEN CONCAT(cat.category_name, ' > ', sub_cat.category_name)
                WHEN cat.category_name IS NOT NULL
                THEN cat.category_name
                ELSE '미분류'
            END AS category_path,
            COALESCE(quiz_count.quiz_mapping_count, 0) AS quiz_mapping_count
        FROM content_master cm
        LEFT JOIN content_category cat ON cm.category_id = cat.category_id AND cat.delete_yn = 'N'
        LEFT JOIN content_category sub_cat ON cm.sub_category_id = sub_cat.category_id AND sub_cat.delete_yn = 'N'
        LEFT JOIN (
            SELECT content_id, COUNT(*) AS quiz_mapping_count
            FROM content_quiz_mapping
            WHERE delete_yn = 'N'
            GROUP BY content_id
        ) quiz_count ON cm.content_id = quiz_count.content_id
        WHERE cm.content_id = #{contentId}
        AND cm.delete_yn = 'N'
    </select>

    <!-- 콘텐츠 코드로 콘텐츠 조회 -->
    <select id="selectContentByCode" resultMap="ContentResultMap">
        SELECT
            cm.content_id,
            cm.title,
            cm.category_id,
            cm.sub_category_id,
            cm.content_code,
            cm.status,
            cm.access_level,
            cm.create_id,
            cm.create_date,
            cm.last_update_id,
            cm.last_update_date,
            cm.delete_yn,
            cm.delete_id,
            cm.delete_date,
            cat.category_name,
            sub_cat.category_name AS sub_category_name,
            CASE
                WHEN cat.category_name IS NOT NULL AND sub_cat.category_name IS NOT NULL
                THEN CONCAT(cat.category_name, ' > ', sub_cat.category_name)
                WHEN cat.category_name IS NOT NULL
                THEN cat.category_name
                ELSE '미분류'
            END AS category_path
        FROM content_master cm
        LEFT JOIN content_category cat ON cm.category_id = cat.category_id AND cat.delete_yn = 'N'
        LEFT JOIN content_category sub_cat ON cm.sub_category_id = sub_cat.category_id AND sub_cat.delete_yn = 'N'
        WHERE cm.content_code = #{contentCode}
        AND cm.delete_yn = 'N'
    </select>

    <!-- 특정 카테고리의 콘텐츠 목록 조회 -->
    <select id="selectContentListByCategoryId" resultMap="ContentResultMap">
        SELECT
            cm.content_id,
            cm.title,
            cm.category_id,
            cm.sub_category_id,
            cm.content_code,
            cm.status,
            cm.access_level,
            cm.create_id,
            cm.create_date,
            cm.last_update_id,
            cm.last_update_date,
            cm.delete_yn,
            cm.delete_id,
            cm.delete_date,
            cat.category_name,
            sub_cat.category_name AS sub_category_name,
            CASE
                WHEN cat.category_name IS NOT NULL AND sub_cat.category_name IS NOT NULL
                THEN CONCAT(cat.category_name, ' > ', sub_cat.category_name)
                WHEN cat.category_name IS NOT NULL
                THEN cat.category_name
                ELSE '미분류'
            END AS category_path
        FROM content_master cm
        LEFT JOIN content_category cat ON cm.category_id = cat.category_id AND cat.delete_yn = 'N'
        LEFT JOIN content_category sub_cat ON cm.sub_category_id = sub_cat.category_id AND sub_cat.delete_yn = 'N'
        WHERE (cm.category_id = #{categoryId} OR cm.sub_category_id = #{categoryId})
        AND cm.delete_yn = 'N'
        ORDER BY cm.content_id DESC
    </select>

    <!-- 특정 카테고리와 그 하위 카테고리의 콘텐츠 목록 조회 -->
    <select id="selectContentListByCategoryIdWithChildren" resultMap="ContentResultMap">
        SELECT
            cm.content_id,
            cm.title,
            cm.category_id,
            cm.sub_category_id,
            cm.content_code,
            cm.status,
            cm.access_level,
            cm.create_id,
            cm.create_date,
            cm.last_update_id,
            cm.last_update_date,
            cm.delete_yn,
            cm.delete_id,
            cm.delete_date,
            cat.category_name,
            sub_cat.category_name AS sub_category_name,
            CASE
                WHEN cat.category_name IS NOT NULL AND sub_cat.category_name IS NOT NULL
                THEN CONCAT(cat.category_name, ' > ', sub_cat.category_name)
                WHEN cat.category_name IS NOT NULL
                THEN cat.category_name
                ELSE '미분류'
            END AS category_path
        FROM content_master cm
        LEFT JOIN content_category cat ON cm.category_id = cat.category_id AND cat.delete_yn = 'N'
        LEFT JOIN content_category sub_cat ON cm.sub_category_id = sub_cat.category_id AND sub_cat.delete_yn = 'N'
        WHERE (cm.category_id = #{categoryId}
               OR cm.sub_category_id = #{categoryId}
               OR cm.category_id IN (
                   SELECT category_id FROM content_category
                   WHERE parent_id = #{categoryId} AND delete_yn = 'N'
               )
               OR cm.sub_category_id IN (
                   SELECT category_id FROM content_category
                   WHERE parent_id = #{categoryId} AND delete_yn = 'N'
               ))
        AND cm.delete_yn = 'N'
        ORDER BY cm.content_id DESC
    </select>

    <!-- 콘텐츠 등록 -->
    <insert id="insertContent" parameterType="kr.wayplus.qr_hallimpark.model.Content" useGeneratedKeys="true" keyProperty="contentId">
        INSERT INTO content_master (
            title,
            category_id,
            sub_category_id,
            content_code,
            status,
            access_level,
            create_id,
            create_date,
            delete_yn
        ) VALUES (
            #{title},
            #{categoryId},
            #{subCategoryId},
            #{contentCode},
            #{status},
            #{accessLevel},
            #{createId},
            NOW(),
            'N'
        )
    </insert>

    <!-- 콘텐츠 수정 -->
    <update id="updateContent" parameterType="kr.wayplus.qr_hallimpark.model.Content">
        UPDATE content_master SET
            title = #{title},
            category_id = #{categoryId},
            sub_category_id = #{subCategoryId},
            content_code = #{contentCode},
            status = #{status},
            access_level = #{accessLevel},
            last_update_id = #{lastUpdateId},
            last_update_date = NOW()
        WHERE content_id = #{contentId}
        AND delete_yn = 'N'
    </update>

    <!-- 콘텐츠 코드만 업데이트 -->
    <update id="updateContentCode">
        UPDATE content_master SET
            content_code = #{contentCode},
            last_update_date = NOW()
        WHERE content_id = #{contentId}
        AND delete_yn = 'N'
    </update>

    <!-- 콘텐츠 상태만 업데이트 -->
    <update id="updateContentStatus">
        UPDATE content_master SET
            status = #{status},
            last_update_id = #{lastUpdateId},
            last_update_date = NOW()
        WHERE content_id = #{contentId}
        AND delete_yn = 'N'
    </update>

    <!-- 콘텐츠 삭제 (논리 삭제) -->
    <update id="deleteContent">
        UPDATE content_master SET
            delete_yn = 'Y',
            delete_id = #{deleteId},
            delete_date = NOW()
        WHERE content_id = #{contentId}
        AND delete_yn = 'N'
    </update>

    <!-- 콘텐츠 코드로 중복 체크 -->
    <select id="countDuplicateContentCode" resultType="int">
        SELECT COUNT(*)
        FROM content_master
        WHERE content_code = #{contentCode}
        AND delete_yn = 'N'
        <if test="excludeContentId != null">
            AND content_id != #{excludeContentId}
        </if>
    </select>

    <!-- 카테고리별 콘텐츠 개수 조회 -->
    <select id="countContentByCategoryId" resultType="int">
        SELECT COUNT(*)
        FROM content_master
        WHERE (category_id = #{categoryId} OR sub_category_id = #{categoryId})
        AND delete_yn = 'N'
    </select>

    <!-- 접근 권한별 개수 조회 -->
    <select id="selectContentCountByAccessLevel" resultMap="ContentResultMap">
        SELECT
            access_level,
            COUNT(*) AS quiz_mapping_count
        FROM content_master
        WHERE delete_yn = 'N'
        GROUP BY access_level
        ORDER BY access_level
    </select>

</mapper>
