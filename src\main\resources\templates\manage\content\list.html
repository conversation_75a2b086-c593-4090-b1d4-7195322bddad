<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/manage}" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <title>콘텐츠 관리 - 한림공원 관리시스템</title>
    <meta name="description" content="콘텐츠를 관리합니다.">
</head>
<th:block layout:fragment="head">
    <link href="/css/manage/common/admin-list.css" rel="stylesheet">
    <link href="/css/manage/content/content.css" rel="stylesheet">
    <link href="/css/manage/content/list.css" rel="stylesheet">
</th:block>
<body>
    <div layout:fragment="content">
        <!-- 페이지 헤더 -->
        <section class="content-page-header">
            <div class="container">
                <div class="row">
                    <div class="col-md-8">
                        <h2>콘텐츠 관리</h2>
                        <p>콘텐츠를 관리합니다.</p>
                    </div>
                    <div class="col-md-4">
                        <a th:href="@{/manage/content/new}" class="btn btn-primary">
                            새 콘텐츠 등록
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 메인 콘텐츠 -->
        <section>
            <div class="container">
                <!-- 서버 메시지는 JavaScript alert로 표시 -->
                <script th:if="${successMessage}" th:inline="javascript">
                    alert(/*[[${successMessage}]]*/ '성공 메시지');
                </script>

                <script th:if="${errorMessage}" th:inline="javascript">
                    alert(/*[[${errorMessage}]]*/ '에러 메시지');
                </script>

                <!-- 최상위 카테고리 필터 -->
                <div class="category-filter-section">
                    <div class="row">
                        <div class="col-12">
                            <div class="category-filter-container">
                                <h5 class="filter-title">카테고리 선택</h5>
                                <div class="category-buttons">
                                    <button type="button" class="btn btn-outline-primary category-btn active" data-category-id="all">
                                        전체
                                    </button>
                                    <button type="button" 
                                            th:each="category : ${rootCategories}"
                                            th:data-category-id="${category.categoryId}"
                                            th:text="${category.categoryName}"
                                            class="btn btn-outline-primary category-btn">
                                        카테고리명
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 콘텐츠 목록 -->
                <!-- 검색/필터 컨트롤 (간단 버전) -->
                <div class="search-controls mb-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="input-group">
                                <select class="form-select" id="searchField" style="max-width: 150px;">
                                    <option value="all">전체</option>
                                    <option value="title">콘텐츠명</option>
                                    <option value="content_code">콘텐츠코드</option>
                                    <option value="category_name">카테고리명</option>
                                </select>
                                <input type="text" class="form-control" id="searchKeyword" placeholder="검색어를 입력하세요">
                                <button class="btn btn-primary" type="button" id="searchBtn">
                                    <i class="fas fa-search"></i> 검색
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex gap-2">
                                <select class="form-select" id="statusFilter" style="max-width: 120px;">
                                    <option value="">전체 상태</option>
                                    <option value="ACTIVE">활성</option>
                                    <option value="INACTIVE">비활성</option>
                                </select>
                                <select class="form-select" id="accessLevelFilter" style="max-width: 150px;">
                                    <option value="">전체 권한</option>
                                    <option value="ALL">전체 접근</option>
                                    <option value="SUPER_ADMIN">슈퍼 관리자</option>
                                    <option value="NORMAL_ADMIN">일반 관리자</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 동적 테이블 컨테이너 -->
                <div class="dynamic-table-container">
                    <!-- 콘텐츠 목록이 여기에 동적으로 로드됩니다 -->
                    <div class="text-center py-5">
                        <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                        <p class="text-muted mt-3">콘텐츠 목록을 불러오는 중...</p>
                    </div>
                </div>

                <!-- 페이징 네비게이션 -->
                <div class="pagination-container">
                    <!-- 페이징이 여기에 동적으로 로드됩니다 -->
                </div>
            </div>
        </section>
    </div>

    <!-- 추가 스크립트 -->
    <th:block layout:fragment="script">
        <script src="/js/manage/content/list.js"></script>
        <script th:inline="javascript">
            // 페이지 설정
            const contentListConfig = {
                currentPage: 1,
                pageSize: 20,
                searchKeyword: '',
                selectedSearchField: 'all',
                sortField: 'content_id',
                sortDirection: 'DESC',
                categoryId: null,
                status: '',
                accessLevel: ''
            };

            // 다국어 버튼 생성 함수
            function generateLanguageButtons(content) {
                const languages = [
                    { code: 'ko', name: '한', supported: content.supportsKorean },
                    { code: 'en', name: '영', supported: content.supportsEnglish },
                    { code: 'ja', name: '일', supported: content.supportsJapanese },
                    { code: 'zh', name: '중', supported: content.supportsChinese }
                ];

                return languages.map(lang => {
                    const isSupported = lang.supported === true || lang.supported === 1;
                    const btnClass = isSupported ? 'btn-primary' : 'btn-outline-secondary';
                    const title = `${lang.name === '한' ? '한국어' : lang.name === '영' ? '영어' : lang.name === '일' ? '일본어' : '중국어'} ${isSupported ? '지원' : '미지원'}`;

                    return `<button type="button" class="btn btn-xs ${btnClass} me-1" title="${title}" style="font-size: 10px; padding: 2px 6px;">${lang.name}</button>`;
                }).join('');
            }

            // 콘텐츠 목록 렌더링 함수
            function renderContentList(items, response) {
                    if (!items || items.length === 0) {
                        return `
                            <div class="text-center py-5">
                                <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                                <p class="text-muted">등록된 콘텐츠가 없습니다.</p>
                            </div>
                        `;
                    }

                    let html = `
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col" style="width: 60px;">고유번호</th>
                                        <th scope="col" style="width: 180px;">콘텐츠명</th>
                                        <th scope="col" style="width: 150px;">카테고리</th>
                                        <th scope="col" style="width: 120px;">서브카테고리</th>
                                        <th scope="col" style="width: 80px;">상태</th>
                                        <th scope="col" style="width: 140px;">다국어 설정</th>
                                        <th scope="col" style="width: 80px;">QR연동</th>
                                        <th scope="col" style="width: 100px;">등록일</th>
                                        <th scope="col" style="width: 100px;">수정일</th>
                                        <th scope="col" style="width: 120px;">관리</th>
                                    </tr>
                                </thead>
                                <tbody>
                    `;

                    items.forEach((content, index) => {
                        const rowNumber = (response.currentPage - 1) * response.pageSize + index + 1;
                        const statusClass = content.status === 'ACTIVE' ? 'text-success' : 'text-danger';
                        const statusText = content.status === 'ACTIVE' ? '활성' : '비활성';
                        const categoryName = content.categoryName || '미분류';
                        const subCategoryName = content.subCategoryName || '-';
                        const qrCount = content.qrMappingCount || 0;
                        const languageSupport = content.languageSupport || '-';

                        // 날짜 형식을 2025-01-01 형식으로 변경
                        const createDate = content.createDate ?
                            new Date(content.createDate).toISOString().split('T')[0] : '-';
                        const lastUpdateDate = content.lastUpdateDate ?
                            new Date(content.lastUpdateDate).toISOString().split('T')[0] : '-';

                        html += `
                            <tr data-content-id="${content.contentId}">
                                <td class="text-center">
                                    <strong>${content.contentCode || content.contentId}</strong>
                                </td>
                                <td>
                                    <div class="content-title-cell">
                                        <strong>${content.title || ''}</strong>
                                        <small class="text-muted d-block">ID: ${content.contentId}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="category-name">${categoryName}</span>
                                </td>
                                <td>
                                    <span class="sub-category-name">${subCategoryName}</span>
                                </td>
                                <td class="text-center">
                                    <button type="button"
                                            class="btn btn-sm status-toggle-btn ${content.status === 'ACTIVE' ? 'btn-success' : 'btn-secondary'}"
                                            data-content-id="${content.contentId}"
                                            data-current-status="${content.status}">
                                        <i class="fas ${content.status === 'ACTIVE' ? 'fa-check' : 'fa-times'}"></i>
                                        ${statusText}
                                    </button>
                                </td>
                                <td class="text-center">
                                    <div class="language-support-cell">
                                        ${generateLanguageButtons(content)}
                                    </div>
                                </td>
                                <td class="text-center">
                                    <span class="badge ${qrCount > 0 ? 'bg-primary' : 'bg-secondary'}">${qrCount}개</span>
                                </td>
                                <td class="text-center">
                                    <small class="text-muted">${createDate}</small>
                                </td>
                                <td class="text-center">
                                    <small class="text-muted">${lastUpdateDate}</small>
                                </td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <button type="button"
                                                class="btn btn-sm btn-outline-warning content-edit-btn"
                                                data-content-id="${content.contentId}"
                                                title="수정">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button type="button"
                                                class="btn btn-sm btn-outline-info content-quiz-btn"
                                                data-content-id="${content.contentId}"
                                                title="문제/게임관리">
                                            <i class="fas fa-gamepad"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `;
                    });

                    html += `
                                </tbody>
                            </table>
                        </div>
                    `;

                    return html;
                }

            // 콘텐츠 목록 로드 함수
            function loadContentList() {
                // URL 파라미터 구성
                const params = new URLSearchParams({
                    page: contentListConfig.currentPage,
                    size: contentListConfig.pageSize,
                    sortField: contentListConfig.sortField,
                    sortDirection: contentListConfig.sortDirection
                });

                // 선택적 파라미터 추가
                if (contentListConfig.searchKeyword) {
                    params.append('searchKeyword', contentListConfig.searchKeyword);
                }
                if (contentListConfig.selectedSearchField && contentListConfig.selectedSearchField !== 'all') {
                    params.append('selectedSearchField', contentListConfig.selectedSearchField);
                }
                if (contentListConfig.categoryId) {
                    params.append('categoryId', contentListConfig.categoryId);
                }
                if (contentListConfig.status) {
                    params.append('status', contentListConfig.status);
                }
                if (contentListConfig.accessLevel) {
                    params.append('accessLevel', contentListConfig.accessLevel);
                }

                // 로딩 표시
                $('.dynamic-table-container').html(`
                    <div class="text-center py-5">
                        <i class="fas fa-spinner fa-spin fa-2x text-muted"></i>
                        <p class="text-muted mt-3">콘텐츠 목록을 불러오는 중...</p>
                    </div>
                `);

                $.ajax({
                    url: '/manage/content/list?' + params.toString(),
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Accept': 'application/json'
                    },
                    success: function(response) {
                        console.log('Response received:', response);

                        // JSON 응답에서 데이터 추출
                        if (response && response.listResponse) {
                            const listResponse = response.listResponse;

                            if (listResponse.success) {
                                const html = renderContentList(listResponse.items || [], listResponse);
                                $('.dynamic-table-container').html(html);

                                // 페이징 업데이트
                                updatePagination(listResponse);
                            } else {
                                $('.dynamic-table-container').html(`
                                    <div class="text-center py-5">
                                        <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                                        <p class="text-muted">데이터를 불러올 수 없습니다.</p>
                                        <p class="text-muted">${listResponse.message || '알 수 없는 오류가 발생했습니다.'}</p>
                                    </div>
                                `);
                            }
                        } else {
                            $('.dynamic-table-container').html(`
                                <div class="text-center py-5">
                                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                                    <p class="text-muted">응답 형식이 올바르지 않습니다.</p>
                                </div>
                            `);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Content list load error:', xhr, status, error);
                        $('.dynamic-table-container').html(`
                            <div class="text-center py-5">
                                <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                                <p class="text-muted">콘텐츠 목록을 불러오는 중 오류가 발생했습니다.</p>
                                <p class="text-muted">상태: ${xhr.status} - ${error}</p>
                            </div>
                        `);
                    }
                });
            }

            // 검색 필드 매핑 함수
            function getSearchFields(selectedField) {
                if (selectedField === 'all') {
                    return ['title', 'content_code', 'category_name'];
                } else {
                    return [selectedField];
                }
            }

            // 페이징 업데이트 함수
            function updatePagination(response) {
                console.log('Pagination info:', {
                    currentPage: response.currentPage,
                    totalPages: response.totalPages,
                    totalCount: response.totalCount
                });

                const paginationContainer = $('.pagination-container');

                if (!response.totalPages || response.totalPages <= 1) {
                    paginationContainer.empty();
                    return;
                }

                let paginationHtml = '<nav aria-label="콘텐츠 목록 페이지네이션"><ul class="pagination justify-content-center">';

                // 이전 페이지 버튼
                if (response.hasPrevious) {
                    paginationHtml += `
                        <li class="page-item">
                            <a class="page-link" href="#" data-page="${response.currentPage - 1}">이전</a>
                        </li>
                    `;
                } else {
                    paginationHtml += '<li class="page-item disabled"><span class="page-link">이전</span></li>';
                }

                // 페이지 번호들
                const startPage = Math.max(1, response.currentPage - 2);
                const endPage = Math.min(response.totalPages, response.currentPage + 2);

                for (let i = startPage; i <= endPage; i++) {
                    if (i === response.currentPage) {
                        paginationHtml += `<li class="page-item active"><span class="page-link">${i}</span></li>`;
                    } else {
                        paginationHtml += `<li class="page-item"><a class="page-link" href="#" data-page="${i}">${i}</a></li>`;
                    }
                }

                // 다음 페이지 버튼
                if (response.hasNext) {
                    paginationHtml += `
                        <li class="page-item">
                            <a class="page-link" href="#" data-page="${response.currentPage + 1}">다음</a>
                        </li>
                    `;
                } else {
                    paginationHtml += '<li class="page-item disabled"><span class="page-link">다음</span></li>';
                }

                paginationHtml += '</ul></nav>';

                // 페이지 정보 표시
                paginationHtml += `
                    <div class="text-center mt-3">
                        <small class="text-muted">
                            총 ${response.totalCount}개 중 ${((response.currentPage - 1) * response.pageSize) + 1}-${Math.min(response.currentPage * response.pageSize, response.totalCount)}개 표시
                        </small>
                    </div>
                `;

                paginationContainer.html(paginationHtml);

                // 페이지 클릭 이벤트 바인딩
                paginationContainer.find('.page-link[data-page]').on('click', function(e) {
                    e.preventDefault();
                    const page = parseInt($(this).data('page'));
                    if (page && page !== contentListConfig.currentPage) {
                        contentListConfig.currentPage = page;
                        loadContentList();
                    }
                });
            }

            // 카테고리 필터 처리 (전역 함수로 정의)
            window.handleCategoryFilter = function(categoryId) {
                contentListConfig.categoryId = categoryId === 'all' ? null : categoryId;
                contentListConfig.currentPage = 1; // 첫 페이지로 리셋
                loadContentList();
            };

            // 검색 실행 함수
            function performSearch() {
                contentListConfig.searchKeyword = $('#searchKeyword').val();
                contentListConfig.selectedSearchField = $('#searchField').val();
                contentListConfig.status = $('#statusFilter').val();
                contentListConfig.accessLevel = $('#accessLevelFilter').val();
                contentListConfig.currentPage = 1; // 첫 페이지로 리셋
                loadContentList();
            }

            // 페이지 초기화
            $(document).ready(function() {
                // 초기 데이터 로드
                loadContentList();

                // 카테고리 버튼 이벤트
                $(document).on('click', '.category-btn', function(e) {
                    e.preventDefault();
                    $('.category-btn').removeClass('active');
                    $(this).addClass('active');

                    const categoryId = $(this).data('category-id');
                    window.handleCategoryFilter(categoryId);
                });

                // 검색 버튼 이벤트
                $('#searchBtn').on('click', performSearch);

                // 검색어 입력 시 엔터키 이벤트
                $('#searchKeyword').on('keypress', function(e) {
                    if (e.which === 13) {
                        performSearch();
                    }
                });

                // 필터 변경 이벤트
                $('#statusFilter, #accessLevelFilter').on('change', performSearch);
            });
        </script>
    </th:block>
</body>
</html>
